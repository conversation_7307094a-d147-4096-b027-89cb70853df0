{"filename": "/home/<USER>/sdlc-agents/backend/__azurite_db_queue_extent__.json", "collections": [{"name": "$EXTENTS_COLLECTION$", "data": [{"id": "0e80d9ea-1c6e-46eb-ae34-4628dd81956d", "locationId": "<PERSON><PERSON><PERSON>", "path": "0e80d9ea-1c6e-46eb-ae34-4628dd81956d", "size": 20163, "lastModifiedInMS": 1753783047642, "meta": {"revision": 5, "created": 1753783047642, "version": 0, "updated": 1753783203248}, "$loki": 7, "LastModifyInMS": 1753783203248}], "idIndex": [7], "binaryIndices": {"id": {"name": "id", "dirty": false, "values": [0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$EXTENTS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 7, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}