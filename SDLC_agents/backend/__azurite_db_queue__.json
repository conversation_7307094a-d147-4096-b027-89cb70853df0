{"filename": "/home/<USER>/sdlc-agents/backend/__azurite_db_queue__.json", "collections": [{"name": "$SERVICES_COLLECTION$", "data": [], "idIndex": null, "binaryIndices": {}, "constraints": null, "uniqueNames": ["accountName"], "transforms": {}, "objType": "$SERVICES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 0, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "$QUEUES_COLLECTION$", "data": [{"accountName": "devstoreaccount1", "name": "durablefunctionshub-workitems", "metadata": {}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 1}, {"accountName": "devstoreaccount1", "name": "durablefunctionshub-control-02", "metadata": {}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 2}, {"accountName": "devstoreaccount1", "name": "durablefunctionshub-control-00", "metadata": {}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 3}, {"accountName": "devstoreaccount1", "name": "durablefunctionshub-control-03", "metadata": {}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 4}, {"accountName": "devstoreaccount1", "name": "durablefunctionshub-control-01", "metadata": {}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 5}], "idIndex": null, "binaryIndices": {"accountName": {"name": "accountName", "dirty": false, "values": [4, 3, 2, 1, 0]}, "name": {"name": "name", "dirty": false, "values": [2, 4, 1, 3, 0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$QUEUES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 5, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "$MESSAGES_COLLECTION$", "data": [], "idIndex": [], "binaryIndices": {"accountName": {"name": "accountName", "dirty": false, "values": []}, "queueName": {"name": "queueName", "dirty": false, "values": []}, "messageId": {"name": "messageId", "dirty": false, "values": []}, "visibleTime": {"name": "visibleTime", "dirty": false, "values": []}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$MESSAGES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 72, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}