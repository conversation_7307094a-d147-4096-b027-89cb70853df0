{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"a7e5562e-ed7f-4292-ac08-d83085b88884","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.ExecutionStartedEvent","OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"2a09aa715ff84b9683b9082b682a687e","ExecutionId":"383c88bb7f254c9584f623a16fe50602"},"EventType":0,"ParentInstance":null,"Name":"DurableFunctionsOrchestrator","Version":"","Input":"\"{\\\"issue_type\\\": \\\"Manual\\\", \\\"input_type\\\": \\\"jira_id\\\", \\\"request_data\\\": \\\"{'Summary': 'Not Given-Develop Grafana Dashboard for Telemetry Data Visualization', 'Description': '', 'Title:** Not Given-Develop Grafana Dashboard for Telemetry Data Visualization   \\\\\\\\n\\\\\\\\n**Description:** \\\\\\\\n*As a* Data Analyst  \\\\\\\\n*I want* to create a Grafana dashboard to visualize telemetry data, including metrics like system performance, error rates, and usage trends  \\\\\\\\n*So that* I can monitor and analyze operational telemetry data effectively to identify issues and optimize performance.\\\\\\\\n\\\\\\\\n *Note: This is AI generated content* \\\\\\\\n\\\\\\\\n**Acceptance Criteria:** \\\\\\\\n**AC01: Grafana Dashboard Creation** \\\\\\\\n*Given* telemetry data is available in the database  \\\\\\\\n*When* a Grafana dashboard is created  \\\\\\\\n*Then* it should display the telemetry data using specific visualization types such as line charts for trends, bar graphs for comparisons, and tables for detailed metrics, with a maximum load time of 2 seconds per visualization.  \\\\\\\\n**AC02: Streamlit App Integration** \\\\\\\\n*Given* the Grafana dashboard is ready  \\\\\\\\n*When* the dashboard is embedded into the Streamlit app  \\\\\\\\n*Then* users should be able to interact with the dashboard, including filtering data by time range, exporting data, and drilling down into specific metrics without performance issues.  \\\\\\\\n**AC03: Real-Time Data Updates** \\\\\\\\n*Given* new telemetry data is added to the database  \\\\\\\\n*When* the Grafana dashboard is refreshed  \\\\\\\\n*Then* the Streamlit app should display the updated data within a refresh rate of 5 seconds, ensuring smooth real-time updates. \\\\\\\\n\\\\\\\\n**Priority:** High \\\\\\\\n\\\\\\\\n**Estimated Effort:** N/A\\\\\\\\n\\\\\\\\n **Acceptance Criteria': 'Not Given', 'parent': 'AA-2'}\\\", \\\"process_type\\\": \\\"get_data\\\", \\\"helper_name\\\": \\\"TCG\\\"}\"","Tags":null,"Correlation":null,"ParentTraceContext":null,"ScheduledStartTime":null,"Generation":0,"EventId":-1,"IsPlayed":false,"Timestamp":"2025-07-29T09:57:27.1745148Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"2a09aa715ff84b9683b9082b682a687e","ExecutionId":"383c88bb7f254c9584f623a16fe50602"},"OrchestrationExecutionContext":null},"CompressedBlobName":null,"SequenceNumber":1,"Sender":{"InstanceId":"","ExecutionId":""},"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"d107af51-3f4c-4e16-87e9-4e2a9f40de36","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.TaskScheduledEvent","EventType":4,"Name":"ExecuteTCG","Version":"","Input":"[\"{\\\"issue_type\\\": \\\"Manual\\\", \\\"input_type\\\": \\\"jira_id\\\", \\\"request_data\\\": \\\"{'Summary': 'Not Given-Develop Grafana Dashboard for Telemetry Data Visualization', 'Description': '', 'Title:** Not Given-Develop Grafana Dashboard for Telemetry Data Visualization   \\\\\\\\n\\\\\\\\n**Description:** \\\\\\\\n*As a* Data Analyst  \\\\\\\\n*I want* to create a Grafana dashboard to visualize telemetry data, including metrics like system performance, error rates, and usage trends  \\\\\\\\n*So that* I can monitor and analyze operational telemetry data effectively to identify issues and optimize performance.\\\\\\\\n\\\\\\\\n *Note: This is AI generated content* \\\\\\\\n\\\\\\\\n**Acceptance Criteria:** \\\\\\\\n**AC01: Grafana Dashboard Creation** \\\\\\\\n*Given* telemetry data is available in the database  \\\\\\\\n*When* a Grafana dashboard is created  \\\\\\\\n*Then* it should display the telemetry data using specific visualization types such as line charts for trends, bar graphs for comparisons, and tables for detailed metrics, with a maximum load time of 2 seconds per visualization.  \\\\\\\\n**AC02: Streamlit App Integration** \\\\\\\\n*Given* the Grafana dashboard is ready  \\\\\\\\n*When* the dashboard is embedded into the Streamlit app  \\\\\\\\n*Then* users should be able to interact with the dashboard, including filtering data by time range, exporting data, and drilling down into specific metrics without performance issues.  \\\\\\\\n**AC03: Real-Time Data Updates** \\\\\\\\n*Given* new telemetry data is added to the database  \\\\\\\\n*When* the Grafana dashboard is refreshed  \\\\\\\\n*Then* the Streamlit app should display the updated data within a refresh rate of 5 seconds, ensuring smooth real-time updates. \\\\\\\\n\\\\\\\\n**Priority:** High \\\\\\\\n\\\\\\\\n**Estimated Effort:** N/A\\\\\\\\n\\\\\\\\n **Acceptance Criteria': 'Not Given', 'parent': 'AA-2'}\\\", \\\"process_type\\\": \\\"get_data\\\", \\\"helper_name\\\": \\\"TCG\\\"}\"]","ParentTraceContext":null,"EventId":0,"IsPlayed":false,"Timestamp":"2025-07-29T09:57:28.0269532Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"2a09aa715ff84b9683b9082b682a687e","ExecutionId":"383c88bb7f254c9584f623a16fe50602"},"OrchestrationExecutionContext":{"$type":"DurableTask.Core.OrchestrationExecutionContext","OrchestrationTags":{"$type":"System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib],[System.String, System.Private.CoreLib]]"}}},"CompressedBlobName":null,"SequenceNumber":2,"Episode":1,"Sender":{"InstanceId":"2a09aa715ff84b9683b9082b682a687e","ExecutionId":"383c88bb7f254c9584f623a16fe50602"},"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"00000000-0000-0000-0000-000000000000","TaskMessage":null,"CompressedBlobName":"2a09aa715ff84b9683b9082b682a687e/message-15417a6ea071435aa14432b0b9892488-TaskCompleted.json.gz","SequenceNumber":0,"Sender":null,"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"49227087-962a-4055-bd3f-b5eba884419e","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.ExecutionStartedEvent","OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"9b77587b8aea4df189d0b2141141ed0c","ExecutionId":"aa8104869b584cf7a6915203f0b59992"},"EventType":0,"ParentInstance":null,"Name":"DurableFunctionsOrchestrator","Version":"","Input":"\"{\\\"issue_type\\\": \\\"Manual\\\", \\\"input_type\\\": \\\"jira_id\\\", \\\"request_data\\\": \\\"TestCaseID:AA-2 - TC 01\\\\nSummary:AA-2 - TC 01 - Verify Grafana dashboard displays telemetry data with specified visualization types and accuracy\\\\nDescription:Ensure that the Grafana dashboard correctly visualizes telemetry data using line charts, bar graphs, and tables, with a maximum load time of 2 seconds per visualization, and the data displayed is accurate.\\\\nManualSteps: \\\\n    Action: Ensure telemetry data is available in the database.\\\\n    Data: Telemetry data including system performance, error rates, and usage trends.\\\\n    Expected Result: Telemetry data is confirmed to be available in the database.\\\\n    Action: Create a Grafana dashboard using the provided telemetry data.\\\\n    Data: Grafana dashboard configuration settings.\\\\n    Expected Result: Grafana dashboard is created successfully.\\\\n    Action: Add visualizations such as line charts, bar graphs, and tables to the dashboard.\\\\n    Data: Visualization types and telemetry data.\\\\n    Expected Result: Visualizations are added and display telemetry data accurately.\\\\n    Action: Verify the load time for each visualization.\\\\n    Data: N/A\\\\n    Expected Result: Load time for each visualization does not exceed 2 seconds.\\\\n    Action: Validate the accuracy of the displayed telemetry data.\\\\n    Data: Expected telemetry data values.\\\\n    Expected Result: Displayed data matches the expected telemetry data values.\\\\n    Action: Test the dashboard with a large dataset.\\\\n    Data: Large telemetry data set.\\\\n    Expected Result: Dashboard handles large datasets without performance degradation.\\\\n    Action: Test the dashboard with missing or corrupted data.\\\\n    Data: Incomplete or corrupted telemetry data.\\\\n    Expected Result: Dashboard handles missing or corrupted data gracefully, displaying appropriate error messages.\\\\nPriority:High\\\\n\\\\n ********** \\\\nTestCaseID:AA-2 - TC 02\\\\nSummary:AA-2 - TC 02 - Validate Streamlit app integration with Grafana dashboard and interaction features\\\\nDescription:Ensure that the Grafana dashboard is embedded into the Streamlit app and users can interact with it seamlessly, including filtering, exporting, and drilling down into data.\\\\nManualSteps: \\\\n    Action: Embed the Grafana dashboard into the Streamlit app.\\\\n    Data: Streamlit app configuration settings.\\\\n    Expected Result: Grafana dashboard is successfully embedded into the Streamlit app.\\\\n    Action: Test interaction features such as filtering data by time range, exporting data, and drilling down into specific metrics.\\\\n    Data: User interaction inputs.\\\\n    Expected Result: Users can interact with the dashboard without performance issues, with response times under 2 seconds.\\\\n    Action: Test interaction features under high user load.\\\\n    Data: Simulated high user load.\\\\n    Expected Result: Dashboard interactions remain responsive under high user load.\\\\n    Action: Test negative scenarios such as failed embedding of the Grafana dashboard.\\\\n    Data: Invalid embedding configuration.\\\\n    Expected Result: Streamlit app displays an appropriate error message when the Grafana dashboard fails to embed.\\\\nPriority:High\\\\n\\\\n ********** \\\\nTestCaseID:AA-2 - TC 03\\\\nSummary:AA-2 - TC 03 - Verify real-time data updates in Grafana dashboard and Streamlit app\\\\nDescription:Ensure that new telemetry data added to the database is displayed in the Grafana dashboard and Streamlit app within a refresh rate of 5 seconds, and handle scenarios like delayed updates or database connection issues.\\\\nManualSteps: \\\\n    Action: Add new telemetry data to the database.\\\\n    Data: New telemetry data entries.\\\\n    Expected Result: New telemetry data is successfully added to the database.\\\\n    Action: Refresh the Grafana dashboard.\\\\n    Data: N/A\\\\n    Expected Result: Grafana dashboard displays updated telemetry data.\\\\n    Action: Verify the Streamlit app displays updated data within a refresh rate of 5 seconds.\\\\n    Data: N/A\\\\n    Expected Result: Streamlit app displays updated data smoothly within the specified refresh rate.\\\\n    Action: Simulate a delay in database updates.\\\\n    Data: Introduce a delay in telemetry data updates.\\\\n    Expected Result: Dashboard displays a message indicating delayed updates and refreshes data once available.\\\\n    Action: Simulate a database connection issue.\\\\n    Data: Disconnect the database.\\\\n    Expected Result: Dashboard displays an appropriate error message indicating a database connection issue.\\\\n    Action: Test the dashboard with high-frequency data updates.\\\\n    Data: High-frequency telemetry data updates.\\\\n    Expected Result: Dashboard handles high-frequency updates without performance degradation.\\\\nPriority:High\\\\n\\\", \\\"process_type\\\": \\\"push_data\\\", \\\"base_url\\\": \\\"https://aiordinate.atlassian.net/\\\", \\\"proj_key\\\": \\\"\\\", \\\"jira_id\\\": \\\"AA-2\\\", \\\"activity\\\": \\\"PushtoJira\\\", \\\"jira_username\\\": \\\"<EMAIL>\\\", \\\"jira_password\\\": \\\"ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC\\\", \\\"helper_name\\\": \\\"TCG\\\"}\"","Tags":null,"Correlation":null,"ParentTraceContext":null,"ScheduledStartTime":null,"Generation":0,"EventId":-1,"IsPlayed":false,"Timestamp":"2025-07-29T10:00:02.4232134Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"9b77587b8aea4df189d0b2141141ed0c","ExecutionId":"aa8104869b584cf7a6915203f0b59992"},"OrchestrationExecutionContext":null},"CompressedBlobName":null,"SequenceNumber":4,"Sender":{"InstanceId":"","ExecutionId":""},"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"a5183b1d-c18d-4a1e-8a96-6d47e8e080bf","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.TaskScheduledEvent","EventType":4,"Name":"PushtoJira","Version":"","Input":"[\"{\\\"issue_type\\\": \\\"Manual\\\", \\\"input_type\\\": \\\"jira_id\\\", \\\"request_data\\\": \\\"TestCaseID:AA-2 - TC 01\\\\nSummary:AA-2 - TC 01 - Verify Grafana dashboard displays telemetry data with specified visualization types and accuracy\\\\nDescription:Ensure that the Grafana dashboard correctly visualizes telemetry data using line charts, bar graphs, and tables, with a maximum load time of 2 seconds per visualization, and the data displayed is accurate.\\\\nManualSteps: \\\\n    Action: Ensure telemetry data is available in the database.\\\\n    Data: Telemetry data including system performance, error rates, and usage trends.\\\\n    Expected Result: Telemetry data is confirmed to be available in the database.\\\\n    Action: Create a Grafana dashboard using the provided telemetry data.\\\\n    Data: Grafana dashboard configuration settings.\\\\n    Expected Result: Grafana dashboard is created successfully.\\\\n    Action: Add visualizations such as line charts, bar graphs, and tables to the dashboard.\\\\n    Data: Visualization types and telemetry data.\\\\n    Expected Result: Visualizations are added and display telemetry data accurately.\\\\n    Action: Verify the load time for each visualization.\\\\n    Data: N/A\\\\n    Expected Result: Load time for each visualization does not exceed 2 seconds.\\\\n    Action: Validate the accuracy of the displayed telemetry data.\\\\n    Data: Expected telemetry data values.\\\\n    Expected Result: Displayed data matches the expected telemetry data values.\\\\n    Action: Test the dashboard with a large dataset.\\\\n    Data: Large telemetry data set.\\\\n    Expected Result: Dashboard handles large datasets without performance degradation.\\\\n    Action: Test the dashboard with missing or corrupted data.\\\\n    Data: Incomplete or corrupted telemetry data.\\\\n    Expected Result: Dashboard handles missing or corrupted data gracefully, displaying appropriate error messages.\\\\nPriority:High\\\\n\\\\n ********** \\\\nTestCaseID:AA-2 - TC 02\\\\nSummary:AA-2 - TC 02 - Validate Streamlit app integration with Grafana dashboard and interaction features\\\\nDescription:Ensure that the Grafana dashboard is embedded into the Streamlit app and users can interact with it seamlessly, including filtering, exporting, and drilling down into data.\\\\nManualSteps: \\\\n    Action: Embed the Grafana dashboard into the Streamlit app.\\\\n    Data: Streamlit app configuration settings.\\\\n    Expected Result: Grafana dashboard is successfully embedded into the Streamlit app.\\\\n    Action: Test interaction features such as filtering data by time range, exporting data, and drilling down into specific metrics.\\\\n    Data: User interaction inputs.\\\\n    Expected Result: Users can interact with the dashboard without performance issues, with response times under 2 seconds.\\\\n    Action: Test interaction features under high user load.\\\\n    Data: Simulated high user load.\\\\n    Expected Result: Dashboard interactions remain responsive under high user load.\\\\n    Action: Test negative scenarios such as failed embedding of the Grafana dashboard.\\\\n    Data: Invalid embedding configuration.\\\\n    Expected Result: Streamlit app displays an appropriate error message when the Grafana dashboard fails to embed.\\\\nPriority:High\\\\n\\\\n ********** \\\\nTestCaseID:AA-2 - TC 03\\\\nSummary:AA-2 - TC 03 - Verify real-time data updates in Grafana dashboard and Streamlit app\\\\nDescription:Ensure that new telemetry data added to the database is displayed in the Grafana dashboard and Streamlit app within a refresh rate of 5 seconds, and handle scenarios like delayed updates or database connection issues.\\\\nManualSteps: \\\\n    Action: Add new telemetry data to the database.\\\\n    Data: New telemetry data entries.\\\\n    Expected Result: New telemetry data is successfully added to the database.\\\\n    Action: Refresh the Grafana dashboard.\\\\n    Data: N/A\\\\n    Expected Result: Grafana dashboard displays updated telemetry data.\\\\n    Action: Verify the Streamlit app displays updated data within a refresh rate of 5 seconds.\\\\n    Data: N/A\\\\n    Expected Result: Streamlit app displays updated data smoothly within the specified refresh rate.\\\\n    Action: Simulate a delay in database updates.\\\\n    Data: Introduce a delay in telemetry data updates.\\\\n    Expected Result: Dashboard displays a message indicating delayed updates and refreshes data once available.\\\\n    Action: Simulate a database connection issue.\\\\n    Data: Disconnect the database.\\\\n    Expected Result: Dashboard displays an appropriate error message indicating a database connection issue.\\\\n    Action: Test the dashboard with high-frequency data updates.\\\\n    Data: High-frequency telemetry data updates.\\\\n    Expected Result: Dashboard handles high-frequency updates without performance degradation.\\\\nPriority:High\\\\n\\\", \\\"process_type\\\": \\\"push_data\\\", \\\"base_url\\\": \\\"https://aiordinate.atlassian.net/\\\", \\\"proj_key\\\": \\\"\\\", \\\"jira_id\\\": \\\"AA-2\\\", \\\"activity\\\": \\\"PushtoJira\\\", \\\"jira_username\\\": \\\"<EMAIL>\\\", \\\"jira_password\\\": \\\"ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC\\\", \\\"helper_name\\\": \\\"TCG\\\"}\"]","ParentTraceContext":null,"EventId":0,"IsPlayed":false,"Timestamp":"2025-07-29T10:00:02.6999991Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"9b77587b8aea4df189d0b2141141ed0c","ExecutionId":"aa8104869b584cf7a6915203f0b59992"},"OrchestrationExecutionContext":{"$type":"DurableTask.Core.OrchestrationExecutionContext","OrchestrationTags":{"$type":"System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib],[System.String, System.Private.CoreLib]]"}}},"CompressedBlobName":null,"SequenceNumber":5,"Episode":1,"Sender":{"InstanceId":"9b77587b8aea4df189d0b2141141ed0c","ExecutionId":"aa8104869b584cf7a6915203f0b59992"},"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"280b6aa9-664a-431c-aeff-4ef09c8fb237","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.TaskCompletedEvent","EventType":5,"TaskScheduledId":0,"Result":"\"Error in PG Service access: 'str' object has no attribute 'status_code'\"","EventId":-1,"IsPlayed":false,"Timestamp":"2025-07-29T10:00:03.0754026Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"9b77587b8aea4df189d0b2141141ed0c","ExecutionId":"aa8104869b584cf7a6915203f0b59992"},"OrchestrationExecutionContext":null},"CompressedBlobName":null,"SequenceNumber":6,"Episode":1,"Sender":{"InstanceId":"9b77587b8aea4df189d0b2141141ed0c","ExecutionId":"aa8104869b584cf7a6915203f0b59992"},"SerializableTraceContext":null}